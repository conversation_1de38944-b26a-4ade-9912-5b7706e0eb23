rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Documents storage
    match /documents/{allPaths=**} {
      // Allow read for authenticated users
      allow read: if request.auth != null;
      // Allow write for authenticated users (their own uploads)
      allow write: if request.auth != null && 
        request.auth.uid != null;
    }
    
    // Profile images storage
    match /profile_images/{userId}/{allPaths=**} {
      // Allow read for authenticated users
      allow read: if request.auth != null;
      // Allow write for the user's own profile image
      allow write: if request.auth != null && 
        request.auth.uid == userId;
    }
  }
}
