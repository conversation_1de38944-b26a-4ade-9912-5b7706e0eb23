import 'package:flutter/material.dart';
import '../models/document_model.dart';

class DocumentProvider extends ChangeNotifier {
  List<DocumentModel> _documents = [];
  List<DocumentModel> _filteredDocuments = [];
  bool _isLoading = false;
  String? _errorMessage;
  String _searchQuery = '';
  String _selectedCategory = 'all';
  String _selectedStatus = 'all';
  String _sortBy = 'uploadedAt';
  bool _sortAscending = false;

  // Getters
  List<DocumentModel> get documents => _filteredDocuments;
  List<DocumentModel> get allDocuments => _documents;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String get searchQuery => _searchQuery;
  String get selectedCategory => _selectedCategory;
  String get selectedStatus => _selectedStatus;
  String get sortBy => _sortBy;
  bool get sortAscending => _sortAscending;

  // Load documents
  Future<void> loadDocuments() async {
    _setLoading(true);
    _clearError();

    try {
      // TODO: Implement document service
      // _documents = await _documentService.getAllDocuments();

      // Temporary sample data for testing
      _documents = _getSampleDocuments();
      _applyFiltersAndSort();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Sample documents for testing
  List<DocumentModel> _getSampleDocuments() {
    final now = DateTime.now();
    return [
      DocumentModel(
        id: '1',
        fileName: 'Surat_Masuk_001_2024.pdf',
        fileSize: 245760, // 240 KB
        fileType: 'application/pdf',
        filePath: 'documents/surat-masuk/Surat_Masuk_001_2024.pdf',
        uploadedBy: 'user-001',
        uploadedAt: now.subtract(const Duration(hours: 2)),
        category: 'cat-001',
        status: 'approved',
        approvedBy: 'admin-001',
        approvedAt: now.subtract(const Duration(hours: 1)),
        permissions: ['admin-001', 'user-001', 'user-002'],
        metadata: DocumentMetadata(
          description:
              'Surat masuk dari Dinas Pendidikan terkait program pelatihan guru',
          tags: ['pendidikan', 'pelatihan', 'guru', 'dinas'],
        ),
      ),
      DocumentModel(
        id: '2',
        fileName: 'Laporan_Keuangan_Q1_2024.xlsx',
        fileSize: 1048576, // 1 MB
        fileType:
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        filePath: 'documents/laporan/Laporan_Keuangan_Q1_2024.xlsx',
        uploadedBy: 'user-002',
        uploadedAt: now.subtract(const Duration(days: 1)),
        category: 'cat-002',
        status: 'pending',
        permissions: ['admin-001', 'user-002'],
        metadata: DocumentMetadata(
          description: 'Laporan keuangan triwulan pertama tahun 2024',
          tags: ['keuangan', 'laporan', 'Q1', '2024'],
        ),
      ),
      DocumentModel(
        id: '3',
        fileName: 'Notulen_Rapat_Koordinasi.docx',
        fileSize: 524288, // 512 KB
        fileType:
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        filePath: 'documents/notulen/Notulen_Rapat_Koordinasi.docx',
        uploadedBy: 'user-001',
        uploadedAt: now.subtract(const Duration(days: 3)),
        category: 'cat-003',
        status: 'approved',
        approvedBy: 'admin-001',
        approvedAt: now.subtract(const Duration(days: 2)),
        permissions: ['admin-001', 'user-001', 'user-003'],
        metadata: DocumentMetadata(
          description: 'Notulen rapat koordinasi bulanan divisi operasional',
          tags: ['notulen', 'rapat', 'koordinasi', 'operasional'],
        ),
      ),
      DocumentModel(
        id: '4',
        fileName: 'Presentasi_Proposal_Proyek.pptx',
        fileSize: 2097152, // 2 MB
        fileType:
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        filePath: 'documents/presentasi/Presentasi_Proposal_Proyek.pptx',
        uploadedBy: 'user-003',
        uploadedAt: now.subtract(const Duration(days: 5)),
        category: 'cat-004',
        status: 'rejected',
        permissions: ['admin-001', 'user-003'],
        metadata: DocumentMetadata(
          description:
              'Presentasi proposal proyek pengembangan sistem informasi',
          tags: ['presentasi', 'proposal', 'proyek', 'sistem'],
        ),
      ),
      DocumentModel(
        id: '5',
        fileName: 'SK_Pengangkatan_Pegawai.pdf',
        fileSize: 327680, // 320 KB
        fileType: 'application/pdf',
        filePath: 'documents/sk/SK_Pengangkatan_Pegawai.pdf',
        uploadedBy: 'admin-001',
        uploadedAt: now.subtract(const Duration(days: 7)),
        category: 'cat-001',
        status: 'approved',
        approvedBy: 'admin-001',
        approvedAt: now.subtract(const Duration(days: 6)),
        permissions: ['admin-001', 'user-001'],
        metadata: DocumentMetadata(
          description: 'Surat Keputusan pengangkatan pegawai baru',
          tags: ['SK', 'pengangkatan', 'pegawai', 'SDM'],
        ),
      ),
    ];
  }

  // Add document
  void addDocument(DocumentModel document) {
    _documents.insert(0, document);
    _applyFiltersAndSort();
  }

  // Update document
  void updateDocument(DocumentModel document) {
    int index = _documents.indexWhere((d) => d.id == document.id);
    if (index != -1) {
      _documents[index] = document;
      _applyFiltersAndSort();
    }
  }

  // Remove document
  void removeDocument(String documentId) {
    _documents.removeWhere((d) => d.id == documentId);
    _applyFiltersAndSort();
  }

  // Search documents
  void searchDocuments(String query) {
    _searchQuery = query;
    _applyFiltersAndSort();
  }

  // Filter by category
  void filterByCategory(String category) {
    _selectedCategory = category;
    _applyFiltersAndSort();
  }

  // Filter by status
  void filterByStatus(String status) {
    _selectedStatus = status;
    _applyFiltersAndSort();
  }

  // Sort documents
  void sortDocuments(String sortBy, {bool ascending = false}) {
    _sortBy = sortBy;
    _sortAscending = ascending;
    _applyFiltersAndSort();
  }

  // Apply filters and sorting
  void _applyFiltersAndSort() {
    _filteredDocuments = _documents.where((document) {
      // Search filter
      bool matchesSearch =
          _searchQuery.isEmpty ||
          document.fileName.toLowerCase().contains(
            _searchQuery.toLowerCase(),
          ) ||
          document.metadata.description.toLowerCase().contains(
            _searchQuery.toLowerCase(),
          ) ||
          document.metadata.tags.any(
            (tag) => tag.toLowerCase().contains(_searchQuery.toLowerCase()),
          );

      // Category filter
      bool matchesCategory =
          _selectedCategory == 'all' || document.category == _selectedCategory;

      // Status filter
      bool matchesStatus =
          _selectedStatus == 'all' || document.status == _selectedStatus;

      return matchesSearch && matchesCategory && matchesStatus;
    }).toList();

    // Apply sorting
    _filteredDocuments.sort((a, b) {
      int comparison = 0;

      switch (_sortBy) {
        case 'fileName':
          comparison = a.fileName.compareTo(b.fileName);
          break;
        case 'fileSize':
          comparison = a.fileSize.compareTo(b.fileSize);
          break;
        case 'uploadedAt':
          comparison = a.uploadedAt.compareTo(b.uploadedAt);
          break;
        case 'category':
          comparison = a.category.compareTo(b.category);
          break;
        case 'status':
          comparison = a.status.compareTo(b.status);
          break;
        default:
          comparison = a.uploadedAt.compareTo(b.uploadedAt);
      }

      return _sortAscending ? comparison : -comparison;
    });

    notifyListeners();
  }

  // Clear filters
  void clearFilters() {
    _searchQuery = '';
    _selectedCategory = 'all';
    _selectedStatus = 'all';
    _applyFiltersAndSort();
  }

  // Get document by ID
  DocumentModel? getDocumentById(String documentId) {
    try {
      return _documents.firstWhere((document) => document.id == documentId);
    } catch (e) {
      return null;
    }
  }

  // Get documents by category
  List<DocumentModel> getDocumentsByCategory(String category) {
    return _documents
        .where((document) => document.category == category)
        .toList();
  }

  // Get documents by status
  List<DocumentModel> getDocumentsByStatus(String status) {
    return _documents.where((document) => document.status == status).toList();
  }

  // Get documents by user
  List<DocumentModel> getDocumentsByUser(String userId) {
    return _documents
        .where((document) => document.uploadedBy == userId)
        .toList();
  }

  // Get recent documents
  List<DocumentModel> getRecentDocuments({int limit = 10}) {
    List<DocumentModel> sortedDocs = List.from(_documents);
    sortedDocs.sort((a, b) => b.uploadedAt.compareTo(a.uploadedAt));
    return sortedDocs.take(limit).toList();
  }

  // Get pending documents count
  int get pendingDocumentsCount {
    return _documents.where((document) => document.status == 'pending').length;
  }

  // Get approved documents count
  int get approvedDocumentsCount {
    return _documents.where((document) => document.status == 'approved').length;
  }

  // Get rejected documents count
  int get rejectedDocumentsCount {
    return _documents.where((document) => document.status == 'rejected').length;
  }

  // Get total documents count
  int get totalDocumentsCount {
    return _documents.length;
  }

  // Get total file size
  int get totalFileSize {
    return _documents.fold(0, (sum, document) => sum + document.fileSize);
  }

  // Get formatted total file size
  String get totalFileSizeFormatted {
    int totalSize = totalFileSize;
    if (totalSize < 1024) {
      return '$totalSize bytes';
    } else if (totalSize < 1024 * 1024) {
      return '${(totalSize / 1024).toStringAsFixed(1)} KB';
    } else if (totalSize < 1024 * 1024 * 1024) {
      return '${(totalSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(totalSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  // Refresh documents
  Future<void> refreshDocuments() async {
    await loadDocuments();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Clear error manually
  void clearError() {
    _clearError();
  }
}
