["C:\\Users\\<USER>\\Documents\\Project_app\\managementdoc\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "C:\\Users\\<USER>\\Documents\\Project_app\\managementdoc\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "C:\\Users\\<USER>\\Documents\\Project_app\\managementdoc\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "C:\\Users\\<USER>\\Documents\\Project_app\\managementdoc\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/Logo.svg", "C:\\Users\\<USER>\\Documents\\Project_app\\managementdoc\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icon/Home.svg", "C:\\Users\\<USER>\\Documents\\Project_app\\managementdoc\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icon/Category.svg", "C:\\Users\\<USER>\\Documents\\Project_app\\managementdoc\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icon/Activity.svg", "C:\\Users\\<USER>\\Documents\\Project_app\\managementdoc\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icon/Profile.svg", "C:\\Users\\<USER>\\Documents\\Project_app\\managementdoc\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icon/Adding.svg", "C:\\Users\\<USER>\\Documents\\Project_app\\managementdoc\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "C:\\Users\\<USER>\\Documents\\Project_app\\managementdoc\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/fluttertoast/assets/toastify.css", "C:\\Users\\<USER>\\Documents\\Project_app\\managementdoc\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/fluttertoast/assets/toastify.js", "C:\\Users\\<USER>\\Documents\\Project_app\\managementdoc\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "C:\\Users\\<USER>\\Documents\\Project_app\\managementdoc\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "C:\\Users\\<USER>\\Documents\\Project_app\\managementdoc\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "C:\\Users\\<USER>\\Documents\\Project_app\\managementdoc\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "C:\\Users\\<USER>\\Documents\\Project_app\\managementdoc\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "C:\\Users\\<USER>\\Documents\\Project_app\\managementdoc\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "C:\\Users\\<USER>\\Documents\\Project_app\\managementdoc\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]