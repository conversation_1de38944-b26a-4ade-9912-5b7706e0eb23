import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:file_selector/file_selector.dart';
import '../models/upload_file_model.dart';
import '../services/file_upload_service.dart';

class UploadProvider with ChangeNotifier {
  final FileUploadService _uploadService = FileUploadService();

  List<UploadFileModel> _uploadQueue = [];
  bool _isUploading = false;

  List<UploadFileModel> get uploadQueue => List.unmodifiable(_uploadQueue);
  bool get isUploading => _isUploading;

  // Statistics
  int get totalFiles => _uploadQueue.length;
  double get averageProgress {
    if (_uploadQueue.isEmpty) return 0.0;
    final totalProgress = _uploadQueue.fold<double>(
      0.0,
      (sum, file) => sum + file.progress,
    );
    return totalProgress / _uploadQueue.length;
  }

  int get completedFiles => _uploadQueue
      .where((file) => file.status == UploadStatus.completed)
      .length;

  int get failedFiles =>
      _uploadQueue.where((file) => file.status == UploadStatus.failed).length;

  // Add files to upload queue
  Future<void> addFiles(List<XFile> files, {String? categoryId}) async {
    for (final file in files) {
      final uploadFile = UploadFileModel.fromXFile(
        file,
        categoryId: categoryId,
      );

      // Get actual file size
      final bytes = await file.readAsBytes();
      final updatedFile = uploadFile.copyWith(fileSize: bytes.length);

      _uploadQueue.add(updatedFile);
    }

    notifyListeners();

    // Start upload process if not already running
    if (!_isUploading) {
      _startUploadProcess();
    }
  }

  // Add single file
  Future<void> addFile(XFile file, {String? categoryId}) async {
    await addFiles([file], categoryId: categoryId);
  }

  // Start upload process
  Future<void> _startUploadProcess() async {
    if (_isUploading) return;

    _isUploading = true;
    notifyListeners();

    final pendingFiles = _uploadQueue
        .where((file) => file.status == UploadStatus.pending)
        .toList();

    // Upload files concurrently (max 3 at a time)
    const maxConcurrent = 3;
    final futures = <Future>[];

    for (int i = 0; i < pendingFiles.length; i += maxConcurrent) {
      final batch = pendingFiles.skip(i).take(maxConcurrent);
      for (final file in batch) {
        futures.add(_uploadFile(file));
      }

      // Wait for current batch to complete before starting next
      if (futures.length >= maxConcurrent) {
        await Future.wait(futures);
        futures.clear();
      }
    }

    // Wait for remaining uploads
    if (futures.isNotEmpty) {
      await Future.wait(futures);
    }

    _isUploading = false;
    notifyListeners();
  }

  // Upload individual file
  Future<void> _uploadFile(UploadFileModel file) async {
    try {
      // Update status to uploading
      _updateFileStatus(file.id, UploadStatus.uploading);
      file.uploadStartTime = DateTime.now();

      // Simulate AI processing for some files
      if (Random().nextBool()) {
        _updateFileAiProcessing(file.id, true);
      }

      // Upload with progress tracking (real Firebase upload)
      await _uploadService.uploadFile(
        file,
        onProgress: (progress) {
          _updateFileProgress(file.id, progress);
        },
      );

      // Mark as completed
      _updateFileStatus(file.id, UploadStatus.completed);
      file.uploadEndTime = DateTime.now();
      _updateFileAiProcessing(file.id, false);
    } catch (e) {
      _updateFileStatus(file.id, UploadStatus.failed);
      _updateFileError(file.id, e.toString());
    }
  }

  // Pause upload
  void pauseUpload(String fileId) {
    final fileIndex = _uploadQueue.indexWhere((f) => f.id == fileId);
    if (fileIndex != -1) {
      final file = _uploadQueue[fileIndex];
      if (file.status == UploadStatus.uploading) {
        _updateFileStatus(fileId, UploadStatus.paused);
        _uploadService.pauseUpload(fileId);
      } else if (file.status == UploadStatus.paused) {
        _updateFileStatus(fileId, UploadStatus.uploading);
        _uploadService.resumeUpload(fileId);
      }
    }
  }

  // Cancel upload
  void cancelUpload(String fileId) {
    final fileIndex = _uploadQueue.indexWhere((f) => f.id == fileId);
    if (fileIndex != -1) {
      _uploadService.cancelUpload(fileId);
      _uploadQueue.removeAt(fileIndex);
      notifyListeners();
    }
  }

  // Retry failed upload
  Future<void> retryUpload(String fileId) async {
    final file = _uploadQueue.firstWhere((f) => f.id == fileId);
    if (file.status == UploadStatus.failed) {
      _updateFileStatus(fileId, UploadStatus.pending);
      _updateFileError(fileId, null);
      _updateFileProgress(fileId, 0.0);

      if (!_isUploading) {
        _startUploadProcess();
      }
    }
  }

  // Clear completed uploads
  void clearCompleted() {
    _uploadQueue.removeWhere((file) => file.status == UploadStatus.completed);
    notifyListeners();
  }

  // Clear all uploads
  void clearAll() {
    // Cancel all active uploads
    for (final file in _uploadQueue) {
      if (file.status == UploadStatus.uploading) {
        _uploadService.cancelUpload(file.id);
      }
    }

    _uploadQueue.clear();
    _isUploading = false;
    notifyListeners();
  }

  // Helper methods to update file properties
  void _updateFileStatus(String fileId, UploadStatus status) {
    final fileIndex = _uploadQueue.indexWhere((f) => f.id == fileId);
    if (fileIndex != -1) {
      _uploadQueue[fileIndex].status = status;
      notifyListeners();
    }
  }

  void _updateFileProgress(String fileId, double progress) {
    final fileIndex = _uploadQueue.indexWhere((f) => f.id == fileId);
    if (fileIndex != -1) {
      _uploadQueue[fileIndex].progress = progress;
      notifyListeners();
    }
  }

  void _updateFileError(String fileId, String? error) {
    final fileIndex = _uploadQueue.indexWhere((f) => f.id == fileId);
    if (fileIndex != -1) {
      _uploadQueue[fileIndex].errorMessage = error;
      notifyListeners();
    }
  }

  void _updateFileAiProcessing(String fileId, bool isProcessing) {
    final fileIndex = _uploadQueue.indexWhere((f) => f.id == fileId);
    if (fileIndex != -1) {
      _uploadQueue[fileIndex].isAiProcessing = isProcessing;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    // Cancel all uploads when provider is disposed
    for (final file in _uploadQueue) {
      if (file.status == UploadStatus.uploading) {
        _uploadService.cancelUpload(file.id);
      }
    }
    super.dispose();
  }
}
