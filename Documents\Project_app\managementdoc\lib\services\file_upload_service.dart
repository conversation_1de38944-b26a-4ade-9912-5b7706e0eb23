import 'dart:async';
import 'dart:math';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/upload_file_model.dart';
import '../models/document_model.dart';
import '../core/services/firebase_service.dart';

class FileUploadService {
  final FirebaseService _firebaseService = FirebaseService.instance;
  final Map<String, StreamSubscription> _uploadSubscriptions = {};
  final Map<String, UploadTask> _uploadTasks = {};

  // Upload file to Firebase Storage and save metadata to Firestore
  Future<String> uploadFile(
    UploadFileModel file, {
    required Function(double) onProgress,
  }) async {
    try {
      // Get current user
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Create storage reference
      final storageRef = _firebaseService.storage
          .ref()
          .child('documents')
          .child('${DateTime.now().millisecondsSinceEpoch}_${file.fileName}');

      // Read file bytes
      final bytes = await file.file.readAsBytes();

      // Create upload task
      final uploadTask = storageRef.putData(
        bytes,
        SettableMetadata(
          contentType: file.fileType,
          customMetadata: {
            'originalName': file.fileName,
            'uploadedBy': currentUser.uid,
            'categoryId': file.categoryId ?? '',
          },
        ),
      );

      // Store upload task for pause/cancel functionality
      _uploadTasks[file.id] = uploadTask;

      // Listen to upload progress
      final subscription = uploadTask.snapshotEvents.listen(
        (TaskSnapshot snapshot) {
          final progress =
              (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
          onProgress(progress);
        },
        onError: (error) {
          throw Exception('Upload failed: $error');
        },
      );

      _uploadSubscriptions[file.id] = subscription;

      // Wait for upload to complete
      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();

      // Save document metadata to Firestore
      await _saveDocumentMetadata(file, downloadUrl, snapshot.ref.fullPath);

      // Clean up
      _cleanup(file.id);

      return downloadUrl;
    } catch (e) {
      _cleanup(file.id);
      rethrow;
    }
  }

  // Save document metadata to Firestore
  Future<void> _saveDocumentMetadata(
    UploadFileModel file,
    String downloadUrl,
    String storagePath,
  ) async {
    // Get current user
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final document = DocumentModel(
      id: file.id,
      fileName: file.fileName,
      fileSize: file.fileSize,
      fileType: file.fileType,
      filePath: storagePath,
      uploadedBy: currentUser.uid,
      uploadedAt: DateTime.now(),
      category: file.categoryId ?? 'uncategorized',
      status: 'pending', // Default status for new uploads
      permissions: [currentUser.uid], // User has access to their own uploads
      metadata: DocumentMetadata(
        description: 'Uploaded via mobile app',
        tags: _generateTags(file.fileName),
      ),
    );

    await _firebaseService.documentsCollection
        .doc(file.id)
        .set(document.toMap());
  }

  // Generate tags based on filename
  List<String> _generateTags(String fileName) {
    final tags = <String>[];
    final nameParts = fileName
        .toLowerCase()
        .replaceAll(RegExp(r'[^a-z0-9\s]'), ' ')
        .split(' ')
        .where((part) => part.isNotEmpty && part.length > 2)
        .toList();

    tags.addAll(nameParts);

    // Add file type tag
    final extension = fileName.split('.').last.toLowerCase();
    tags.add(extension);

    return tags.take(10).toList(); // Limit to 10 tags
  }

  // Pause upload
  void pauseUpload(String fileId) {
    final uploadTask = _uploadTasks[fileId];
    if (uploadTask != null) {
      uploadTask.pause();
    }
  }

  // Resume upload
  void resumeUpload(String fileId) {
    final uploadTask = _uploadTasks[fileId];
    if (uploadTask != null) {
      uploadTask.resume();
    }
  }

  // Cancel upload
  void cancelUpload(String fileId) {
    final uploadTask = _uploadTasks[fileId];
    if (uploadTask != null) {
      uploadTask.cancel();
    }
    _cleanup(fileId);
  }

  // Clean up resources
  void _cleanup(String fileId) {
    _uploadSubscriptions[fileId]?.cancel();
    _uploadSubscriptions.remove(fileId);
    _uploadTasks.remove(fileId);
  }

  // Simulate upload progress for demo purposes
  Future<void> simulateUpload(
    UploadFileModel file, {
    required Function(double) onProgress,
  }) async {
    const totalSteps = 100;
    const stepDuration = Duration(milliseconds: 50);

    for (int i = 0; i <= totalSteps; i++) {
      // Check if upload was cancelled
      if (!_uploadTasks.containsKey(file.id)) {
        throw Exception('Upload cancelled');
      }

      // Simulate variable upload speed
      final progress = i.toDouble();
      onProgress(progress);

      if (i < totalSteps) {
        await Future.delayed(stepDuration);

        // Simulate occasional slowdowns
        if (Random().nextInt(10) == 0) {
          await Future.delayed(const Duration(milliseconds: 200));
        }
      }
    }

    // Simulate saving metadata
    await Future.delayed(const Duration(milliseconds: 500));
  }

  // Get upload statistics
  Map<String, dynamic> getUploadStats() {
    return {
      'activeUploads': _uploadTasks.length,
      'totalSubscriptions': _uploadSubscriptions.length,
    };
  }

  // Dispose all resources
  void dispose() {
    for (final subscription in _uploadSubscriptions.values) {
      subscription.cancel();
    }
    _uploadSubscriptions.clear();

    for (final task in _uploadTasks.values) {
      task.cancel();
    }
    _uploadTasks.clear();
  }
}
