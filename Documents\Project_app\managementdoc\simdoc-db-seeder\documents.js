const { db, COLLECTIONS, generateTimestamp, generateId } = require('./config');

// Sample documents data
const documentsData = [
  {
    id: 'doc-001',
    fileName: 'Surat_Masuk_001_2024.pdf',
    fileSize: 245760, // 240 KB
    fileType: 'application/pdf',
    filePath: 'documents/surat-masuk/Surat_Masuk_001_2024.pdf',
    uploadedBy: 'user-001',
    uploadedAt: generateTimestamp(15),
    category: 'cat-001',
    status: 'approved',
    approvedBy: 'admin-001',
    approvedAt: generateTimestamp(14),
    permissions: ['admin-001', 'user-001', 'user-002'],
    metadata: {
      description: 'Surat masuk dari Dinas Pendidikan terkait program pelatihan guru',
      tags: ['pendidikan', 'pelatihan', 'guru', 'dinas']
    }
  },
  {
    id: 'doc-002',
    fileName: 'SK_Pengangkatan_Pegawai_2024.pdf',
    fileSize: 512000, // 500 KB
    fileType: 'application/pdf',
    filePath: 'documents/surat-keputusan/SK_Pengangkatan_Pegawai_2024.pdf',
    uploadedBy: 'admin-001',
    uploadedAt: generateTimestamp(12),
    category: 'cat-002',
    status: 'approved',
    approvedBy: 'admin-001',
    approvedAt: generateTimestamp(12),
    permissions: ['admin-001', 'user-001'],
    metadata: {
      description: 'Surat Keputusan pengangkatan pegawai baru periode 2024',
      tags: ['sk', 'pengangkatan', 'pegawai', 'sdm']
    }
  },
  {
    id: 'doc-003',
    fileName: 'Notulen_Rapat_Koordinasi_Jan2024.docx',
    fileSize: 128000, // 125 KB
    fileType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    filePath: 'documents/notulen/Notulen_Rapat_Koordinasi_Jan2024.docx',
    uploadedBy: 'user-002',
    uploadedAt: generateTimestamp(10),
    category: 'cat-003',
    status: 'pending',
    approvedBy: null,
    approvedAt: null,
    permissions: ['admin-001', 'user-002', 'user-003'],
    metadata: {
      description: 'Notulen rapat koordinasi bulanan Januari 2024',
      tags: ['notulen', 'rapat', 'koordinasi', 'januari']
    }
  },
  {
    id: 'doc-004',
    fileName: 'Laporan_Evaluasi_Q4_2023.xlsx',
    fileSize: 1048576, // 1 MB
    fileType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    filePath: 'documents/laporan/Laporan_Evaluasi_Q4_2023.xlsx',
    uploadedBy: 'user-001',
    uploadedAt: generateTimestamp(8),
    category: 'cat-004',
    status: 'approved',
    approvedBy: 'admin-001',
    approvedAt: generateTimestamp(7),
    permissions: ['admin-001', 'user-001'],
    metadata: {
      description: 'Laporan evaluasi kinerja kuartal 4 tahun 2023',
      tags: ['laporan', 'evaluasi', 'q4', '2023', 'kinerja']
    }
  },
  {
    id: 'doc-005',
    fileName: 'Surat_Keluar_Undangan_Seminar.pdf',
    fileSize: 320000, // 312 KB
    fileType: 'application/pdf',
    filePath: 'documents/surat-keluar/Surat_Keluar_Undangan_Seminar.pdf',
    uploadedBy: 'user-003',
    uploadedAt: generateTimestamp(6),
    category: 'cat-005',
    status: 'approved',
    approvedBy: 'admin-001',
    approvedAt: generateTimestamp(5),
    permissions: ['admin-001', 'user-002', 'user-003'],
    metadata: {
      description: 'Surat undangan seminar nasional pendidikan',
      tags: ['undangan', 'seminar', 'nasional', 'pendidikan']
    }
  },
  {
    id: 'doc-006',
    fileName: 'Proposal_Workshop_Teknologi.pdf',
    fileSize: 768000, // 750 KB
    fileType: 'application/pdf',
    filePath: 'documents/proposal/Proposal_Workshop_Teknologi.pdf',
    uploadedBy: 'user-002',
    uploadedAt: generateTimestamp(4),
    category: 'cat-006',
    status: 'pending',
    approvedBy: null,
    approvedAt: null,
    permissions: ['admin-001', 'user-001', 'user-002'],
    metadata: {
      description: 'Proposal workshop teknologi informasi untuk pegawai',
      tags: ['proposal', 'workshop', 'teknologi', 'ti']
    }
  },
  {
    id: 'doc-007',
    fileName: 'Laporan_Keuangan_Januari_2024.pdf',
    fileSize: 456000, // 445 KB
    fileType: 'application/pdf',
    filePath: 'documents/keuangan/Laporan_Keuangan_Januari_2024.pdf',
    uploadedBy: 'admin-001',
    uploadedAt: generateTimestamp(3),
    category: 'cat-007',
    status: 'approved',
    approvedBy: 'admin-001',
    approvedAt: generateTimestamp(3),
    permissions: ['admin-001'],
    metadata: {
      description: 'Laporan keuangan bulanan Januari 2024',
      tags: ['keuangan', 'laporan', 'januari', 'anggaran']
    }
  },
  {
    id: 'doc-008',
    fileName: 'Surat_Masuk_Komplain_Layanan.pdf',
    fileSize: 180000, // 175 KB
    fileType: 'application/pdf',
    filePath: 'documents/surat-masuk/Surat_Masuk_Komplain_Layanan.pdf',
    uploadedBy: 'user-001',
    uploadedAt: generateTimestamp(2),
    category: 'cat-001',
    status: 'rejected',
    approvedBy: 'admin-001',
    approvedAt: generateTimestamp(1),
    permissions: ['admin-001', 'user-001'],
    metadata: {
      description: 'Surat komplain layanan dari masyarakat',
      tags: ['komplain', 'layanan', 'masyarakat', 'keluhan']
    }
  }
];

async function seedDocuments() {
  console.log('🚀 Starting documents seeding...');
  
  try {
    const batch = db.batch();
    
    for (const document of documentsData) {
      const docRef = db.collection(COLLECTIONS.DOCUMENTS).doc(document.id);
      const { id, ...docData } = document;
      batch.set(docRef, docData);
    }
    
    await batch.commit();
    console.log('✅ Documents collection seeded successfully!');
    console.log(`📊 Total documents created: ${documentsData.length}`);
    
    // Display documents summary by status
    const statusCount = documentsData.reduce((acc, doc) => {
      acc[doc.status] = (acc[doc.status] || 0) + 1;
      return acc;
    }, {});
    
    console.log('\n📋 Documents Summary by Status:');
    Object.entries(statusCount).forEach(([status, count]) => {
      const emoji = status === 'approved' ? '✅' : status === 'pending' ? '⏳' : '❌';
      console.log(`  ${emoji} ${status}: ${count} documents`);
    });
    
  } catch (error) {
    console.error('❌ Error seeding documents:', error);
  }
}

// Run if called directly
if (require.main === module) {
  seedDocuments().then(() => {
    console.log('🎉 Documents seeding completed!');
    process.exit(0);
  }).catch((error) => {
    console.error('💥 Documents seeding failed:', error);
    process.exit(1);
  });
}

module.exports = { seedDocuments, documentsData };
