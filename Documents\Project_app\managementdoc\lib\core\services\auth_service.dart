import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/firebase_service.dart';
import '../utils/async_utils.dart';
import '../../models/user_model.dart';
import '../../models/activity_model.dart';

class AuthService {
  static AuthService? _instance;
  static AuthService get instance => _instance ??= AuthService._();

  AuthService._();

  final FirebaseService _firebaseService = FirebaseService.instance;

  // Get current user
  User? get currentUser => _firebaseService.auth.currentUser;

  // Get current user stream
  Stream<User?> get authStateChanges =>
      _firebaseService.auth.authStateChanges();

  // Login with email and password
  Future<UserModel?> login(
    String email,
    String password, {
    bool rememberMe = false,
  }) async {
    try {
      // Sign in with Firebase Auth
      UserCredential userCredential = await _firebaseService.auth
          .signInWithEmailAndPassword(email: email, password: password);

      if (userCredential.user != null) {
        // Get user data from Firestore
        DocumentSnapshot userDoc = await _firebaseService.usersCollection
            .doc(userCredential.user!.uid)
            .get();

        if (userDoc.exists) {
          try {
            UserModel user = UserModel.fromFirestore(userDoc);

            // Check if user is active
            if (!user.isActive) {
              await logout();
              throw Exception('Akun Anda tidak aktif. Hubungi administrator.');
            }

            // Update last login
            await _updateLastLogin(user.id);

            // Log activity
            await _logActivity(user.id, ActivityType.login, 'System Login');

            // Save remember me preference
            if (rememberMe) {
              await _saveRememberMe(email);
            } else {
              await _clearRememberMe();
            }

            // Save login session for persistence
            await _saveLoginSession(user.id);

            return user;
          } catch (e) {
            await logout();
            throw Exception('Error parsing user data: ${e.toString()}');
          }
        } else {
          // User exists in Firebase Auth but not in Firestore
          // This might happen if user was created but Firestore document wasn't created
          await logout();
          throw Exception(
            'Data pengguna tidak ditemukan di database. Silakan hubungi administrator untuk melengkapi data akun Anda.',
          );
        }
      }

      return null;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('Terjadi kesalahan: ${e.toString()}');
    }
  }

  // Logout
  Future<void> logout() async {
    try {
      String? userId = currentUser?.uid;

      // Log activity before logout
      if (userId != null) {
        await _logActivity(userId, ActivityType.logout, 'System Logout');
      }

      // Clear login session
      await _clearLoginSession();

      await _firebaseService.auth.signOut();
    } catch (e) {
      throw Exception('Gagal logout: ${e.toString()}');
    }
  }

  // Get current user data
  Future<UserModel?> getCurrentUserData() async {
    try {
      if (currentUser == null) return null;

      DocumentSnapshot userDoc = await _firebaseService.usersCollection
          .doc(currentUser!.uid)
          .get();

      if (userDoc.exists) {
        try {
          return UserModel.fromFirestore(userDoc);
        } catch (e) {
          throw Exception('Error parsing user data: ${e.toString()}');
        }
      }

      return null;
    } catch (e) {
      throw Exception('Gagal mengambil data pengguna: ${e.toString()}');
    }
  }

  // Check if user is logged in
  bool get isLoggedIn => currentUser != null;

  // Update last login
  Future<void> _updateLastLogin(String userId) async {
    await _firebaseService.usersCollection.doc(userId).update({
      'lastLogin': FieldValue.serverTimestamp(),
    });
  }

  // Log user activity
  Future<void> _logActivity(
    String userId,
    ActivityType action,
    String resource,
  ) async {
    try {
      ActivityModel activity = ActivityModel(
        id: '',
        userId: userId,
        action: action.value,
        resource: resource,
        timestamp: DateTime.now(),
        details: {'userAgent': 'Flutter App', 'platform': 'Mobile'},
      );

      await _firebaseService.activitiesCollection.add(activity.toMap());
    } catch (e) {
      // Don't throw error for activity logging
      print('Failed to log activity: $e');
    }
  }

  // Save remember me preference
  Future<void> _saveRememberMe(String email) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('remembered_email', email);
    await prefs.setBool('remember_me', true);
  }

  // Clear remember me preference
  Future<void> _clearRememberMe() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('remembered_email');
    await prefs.setBool('remember_me', false);
  }

  // Get remembered email
  Future<String?> getRememberedEmail() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    bool rememberMe = prefs.getBool('remember_me') ?? false;
    if (rememberMe) {
      return prefs.getString('remembered_email');
    }
    return null;
  }

  // Check if remember me is enabled
  Future<bool> isRememberMeEnabled() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool('remember_me') ?? false;
  }

  // Save login session
  Future<void> _saveLoginSession(String userId) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('logged_in_user_id', userId);
    await prefs.setBool('is_logged_in', true);
    await prefs.setInt(
      'login_timestamp',
      DateTime.now().millisecondsSinceEpoch,
    );
  }

  // Clear login session
  Future<void> _clearLoginSession() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('logged_in_user_id');
    await prefs.setBool('is_logged_in', false);
    await prefs.remove('login_timestamp');
  }

  // Check if user has valid session
  Future<bool> hasValidSession() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    bool isLoggedIn = prefs.getBool('is_logged_in') ?? false;

    if (!isLoggedIn || currentUser == null) {
      return false;
    }

    // Check if session is not too old (optional: 30 days)
    int? loginTimestamp = prefs.getInt('login_timestamp');
    if (loginTimestamp != null) {
      DateTime loginTime = DateTime.fromMillisecondsSinceEpoch(loginTimestamp);
      Duration sessionAge = DateTime.now().difference(loginTime);

      // Session expires after 30 days of inactivity
      if (sessionAge.inDays > 30) {
        await _clearLoginSession();
        return false;
      }
    }

    return true;
  }

  // Update session timestamp (call this when user is active)
  Future<void> updateSessionActivity() async {
    if (currentUser != null) {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setInt(
        'login_timestamp',
        DateTime.now().millisecondsSinceEpoch,
      );
    }
  }

  // Handle Firebase Auth exceptions
  String _handleAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'Email tidak terdaftar. Silakan hubungi administrator.';
      case 'wrong-password':
        return 'Password salah. Silakan coba lagi.';
      case 'invalid-email':
        return 'Format email tidak valid.';
      case 'user-disabled':
        return 'Akun telah dinonaktifkan. Hubungi administrator.';
      case 'too-many-requests':
        return 'Terlalu banyak percobaan login. Coba lagi dalam beberapa menit.';
      case 'network-request-failed':
        return 'Tidak ada koneksi internet. Periksa koneksi Anda.';
      case 'invalid-credential':
        return 'Email atau password salah. Silakan coba lagi.';
      case 'credential-already-in-use':
        return 'Kredensial sudah digunakan oleh akun lain.';
      case 'invalid-verification-code':
        return 'Kode verifikasi tidak valid.';
      case 'invalid-verification-id':
        return 'ID verifikasi tidak valid.';
      case 'session-cookie-expired':
        return 'Sesi telah berakhir. Silakan login kembali.';
      case 'uid-already-exists':
        return 'UID pengguna sudah ada.';
      case 'email-already-in-use':
        return 'Email sudah digunakan oleh akun lain.';
      case 'phone-number-already-exists':
        return 'Nomor telepon sudah digunakan.';
      case 'project-not-found':
        return 'Proyek Firebase tidak ditemukan.';
      case 'insufficient-permission':
        return 'Izin tidak mencukupi.';
      case 'internal-error':
        return 'Terjadi kesalahan internal. Coba lagi nanti.';
      default:
        return 'Terjadi kesalahan: ${e.message ?? e.code}';
    }
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _firebaseService.auth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('Gagal mengirim email reset password: ${e.toString()}');
    }
  }

  // Change password
  Future<void> changePassword(
    String currentPassword,
    String newPassword,
  ) async {
    try {
      if (currentUser == null) {
        throw Exception('Pengguna tidak ditemukan.');
      }

      // Re-authenticate user
      AuthCredential credential = EmailAuthProvider.credential(
        email: currentUser!.email!,
        password: currentPassword,
      );

      await currentUser!.reauthenticateWithCredential(credential);

      // Update password
      await currentUser!.updatePassword(newPassword);

      // Log activity
      await _logActivity(
        currentUser!.uid,
        ActivityType.update,
        'Password Changed',
      );
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('Gagal mengubah password: ${e.toString()}');
    }
  }
}
