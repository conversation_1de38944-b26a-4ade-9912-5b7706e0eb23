const { db, COLLECTIONS, generateTimestamp } = require('./config');

// Sample categories data
const categoriesData = [
  {
    id: 'cat-001',
    name: 'Surat Masuk',
    description: 'Dokumen surat masuk dari berbagai instansi dan pihak eksternal',
    createdBy: 'admin-001',
    createdAt: generateTimestamp(30),
    permissions: ['admin-001', 'user-001', 'user-002', 'user-003'],
    isActive: true
  },
  {
    id: 'cat-002',
    name: 'Surat Keputusan',
    description: 'Dokumen surat keputusan resmi dari pimpinan organisasi',
    createdBy: 'admin-001',
    createdAt: generateTimestamp(28),
    permissions: ['admin-001', 'user-001', 'user-002'],
    isActive: true
  },
  {
    id: 'cat-003',
    name: 'Notulen Rapat',
    description: 'Dokumen notulen hasil rapat dan pertemuan resmi',
    createdBy: 'admin-001',
    createdAt: generateTimestamp(25),
    permissions: ['admin-001', 'user-001', 'user-002', 'user-003'],
    isActive: true
  },
  {
    id: 'cat-004',
    name: 'Laporan Evaluasi',
    description: 'Dokumen laporan evaluasi kegiatan dan program kerja',
    createdBy: 'admin-001',
    createdAt: generateTimestamp(22),
    permissions: ['admin-001', 'user-001'],
    isActive: true
  },
  {
    id: 'cat-005',
    name: 'Surat Keluar',
    description: 'Dokumen surat keluar yang dikirim ke pihak eksternal',
    createdBy: 'admin-001',
    createdAt: generateTimestamp(20),
    permissions: ['admin-001', 'user-002', 'user-003'],
    isActive: true
  },
  {
    id: 'cat-006',
    name: 'Proposal Kegiatan',
    description: 'Dokumen proposal untuk berbagai kegiatan dan program',
    createdBy: 'admin-001',
    createdAt: generateTimestamp(18),
    permissions: ['admin-001', 'user-001', 'user-002'],
    isActive: true
  },
  {
    id: 'cat-007',
    name: 'Dokumen Keuangan',
    description: 'Dokumen terkait keuangan dan anggaran organisasi',
    createdBy: 'admin-001',
    createdAt: generateTimestamp(15),
    permissions: ['admin-001'],
    isActive: true
  },
  {
    id: 'cat-008',
    name: 'Arsip Lama',
    description: 'Kategori untuk dokumen arsip yang sudah tidak aktif',
    createdBy: 'admin-001',
    createdAt: generateTimestamp(10),
    permissions: ['admin-001'],
    isActive: false
  }
];

async function seedCategories() {
  console.log('🚀 Starting categories seeding...');
  
  try {
    const batch = db.batch();
    
    for (const category of categoriesData) {
      const categoryRef = db.collection(COLLECTIONS.CATEGORIES).doc(category.id);
      const { id, ...categoryData } = category;
      batch.set(categoryRef, categoryData);
    }
    
    await batch.commit();
    console.log('✅ Categories collection seeded successfully!');
    console.log(`📊 Total categories created: ${categoriesData.length}`);
    
    // Display categories summary
    console.log('\n📋 Categories Summary:');
    categoriesData.forEach(cat => {
      const status = cat.isActive ? '🟢 Active' : '🔴 Inactive';
      console.log(`  ${status} ${cat.name} - ${cat.permissions.length} users`);
    });
    
  } catch (error) {
    console.error('❌ Error seeding categories:', error);
  }
}

// Run if called directly
if (require.main === module) {
  seedCategories().then(() => {
    console.log('🎉 Categories seeding completed!');
    process.exit(0);
  }).catch((error) => {
    console.error('💥 Categories seeding failed:', error);
    process.exit(1);
  });
}

module.exports = { seedCategories, categoriesData };
