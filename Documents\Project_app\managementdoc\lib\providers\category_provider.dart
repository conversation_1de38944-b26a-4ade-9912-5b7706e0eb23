import 'package:flutter/material.dart';
import '../models/category_model.dart';
import '../core/services/category_service.dart';

class CategoryProvider extends ChangeNotifier {
  final CategoryService _categoryService = CategoryService();
  List<CategoryModel> _categories = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<CategoryModel> get categories => _categories;
  List<CategoryModel> get activeCategories =>
      _categories.where((c) => c.isActive).toList();
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Load categories
  Future<void> loadCategories() async {
    _setLoading(true);
    _clearError();

    try {
      // Try to load from Firebase first
      try {
        _categories = await _categoryService.getAllCategories();
      } catch (firebaseError) {
        // If Firebase fails, use sample data
        print('Firebase error, using sample data: $firebaseError');
        _categories = _getSampleCategories();
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Sample categories for testing (matching document categories)
  List<CategoryModel> _getSampleCategories() {
    final now = DateTime.now();
    return [
      CategoryModel(
        id: 'cat-001',
        name: 'Surat Masuk',
        description:
            'Dokumen surat masuk dari berbagai instansi dan pihak eksternal',
        createdBy: 'admin-001',
        createdAt: now.subtract(const Duration(days: 30)),
        permissions: ['admin-001', 'user-001', 'user-002', 'user-003'],
        isActive: true,
      ),
      CategoryModel(
        id: 'cat-002',
        name: 'Laporan Keuangan',
        description: 'Dokumen laporan keuangan dan anggaran organisasi',
        createdBy: 'admin-001',
        createdAt: now.subtract(const Duration(days: 28)),
        permissions: ['admin-001', 'user-002'],
        isActive: true,
      ),
      CategoryModel(
        id: 'cat-003',
        name: 'Notulen Rapat',
        description: 'Dokumen notulen hasil rapat dan pertemuan resmi',
        createdBy: 'admin-001',
        createdAt: now.subtract(const Duration(days: 25)),
        permissions: ['admin-001', 'user-001', 'user-002', 'user-003'],
        isActive: true,
      ),
      CategoryModel(
        id: 'cat-004',
        name: 'Proposal Proyek',
        description: 'Dokumen proposal untuk berbagai proyek dan program',
        createdBy: 'admin-001',
        createdAt: now.subtract(const Duration(days: 22)),
        permissions: ['admin-001', 'user-003'],
        isActive: true,
      ),
      CategoryModel(
        id: 'cat-005',
        name: 'Surat Keputusan',
        description: 'Dokumen surat keputusan resmi dari pimpinan organisasi',
        createdBy: 'admin-001',
        createdAt: now.subtract(const Duration(days: 20)),
        permissions: ['admin-001', 'user-001'],
        isActive: true,
      ),
    ];
  }

  // Add category
  Future<void> addCategory(CategoryModel category) async {
    try {
      // Add to Firebase first
      final categoryId = await _categoryService.addCategory(category);

      // Update local list with the new ID
      final updatedCategory = category.copyWith(id: categoryId);
      _categories.insert(0, updatedCategory);
      notifyListeners();
    } catch (e) {
      // If Firebase fails, add locally only
      _categories.insert(0, category);
      notifyListeners();
      rethrow;
    }
  }

  // Update category
  Future<void> updateCategory(CategoryModel category) async {
    try {
      // Update in Firebase first
      await _categoryService.updateCategory(category.id, category);

      // Update local list
      int index = _categories.indexWhere((c) => c.id == category.id);
      if (index != -1) {
        _categories[index] = category;
        notifyListeners();
      }
    } catch (e) {
      // If Firebase fails, update locally only
      int index = _categories.indexWhere((c) => c.id == category.id);
      if (index != -1) {
        _categories[index] = category;
        notifyListeners();
      }
      rethrow;
    }
  }

  // Remove category
  Future<void> removeCategory(String categoryId) async {
    try {
      // Remove from Firebase first
      await _categoryService.deleteCategory(categoryId);

      // Remove from local list
      _categories.removeWhere((c) => c.id == categoryId);
      notifyListeners();
    } catch (e) {
      // If Firebase fails, remove locally only
      _categories.removeWhere((c) => c.id == categoryId);
      notifyListeners();
      rethrow;
    }
  }

  // Toggle category status
  void toggleCategoryStatus(String categoryId) {
    int index = _categories.indexWhere((c) => c.id == categoryId);
    if (index != -1) {
      _categories[index] = _categories[index].copyWith(
        isActive: !_categories[index].isActive,
      );
      notifyListeners();
    }
  }

  // Get category by ID
  CategoryModel? getCategoryById(String categoryId) {
    try {
      return _categories.firstWhere((category) => category.id == categoryId);
    } catch (e) {
      return null;
    }
  }

  // Get category by name
  CategoryModel? getCategoryByName(String name) {
    try {
      return _categories.firstWhere((category) => category.name == name);
    } catch (e) {
      return null;
    }
  }

  // Get categories that user has access to
  List<CategoryModel> getCategoriesForUser(String userId) {
    return _categories.where((category) {
      return category.isActive &&
          (category.permissions.isEmpty || category.hasPermission(userId));
    }).toList();
  }

  // Get total categories count
  int get totalCategoriesCount {
    return _categories.length;
  }

  // Get active categories count
  int get activeCategoriesCount {
    return _categories.where((category) => category.isActive).length;
  }

  // Get inactive categories count
  int get inactiveCategoriesCount {
    return _categories.where((category) => !category.isActive).length;
  }

  // Search categories
  List<CategoryModel> searchCategories(String query) {
    if (query.isEmpty) return _categories;

    return _categories.where((category) {
      return category.name.toLowerCase().contains(query.toLowerCase()) ||
          category.description.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  // Refresh categories
  Future<void> refreshCategories() async {
    await loadCategories();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Clear error manually
  void clearError() {
    _clearError();
  }
}
