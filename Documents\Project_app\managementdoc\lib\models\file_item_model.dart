class FileItem {
  final String id;
  final String name;
  final String owner;
  final String size;
  final String date;
  final String type;
  final String? downloadUrl;

  FileItem({
    required this.id,
    required this.name,
    required this.owner,
    required this.size,
    required this.date,
    required this.type,
    this.downloadUrl,
  });

  factory FileItem.fromJson(Map<String, dynamic> json) {
    return FileItem(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      owner: json['owner'] ?? '',
      size: json['size'] ?? '',
      date: json['date'] ?? '',
      type: json['type'] ?? '',
      downloadUrl: json['downloadUrl'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'owner': owner,
      'size': size,
      'date': date,
      'type': type,
      'downloadUrl': downloadUrl,
    };
  }

  // Sample data for demonstration
  static List<FileItem> getSampleFiles() {
    return [
      FileItem(
        id: '1',
        name: 'Document.pdf',
        owner: 'Admin',
        size: '2.4Gb',
        date: '2015/09/24',
        type: 'pdf',
        downloadUrl: 'https://example.com/document1.pdf',
      ),
      FileItem(
        id: '2',
        name: 'Presentation.pdf',
        owner: 'Me',
        size: '238Mb',
        date: '2012/10/13',
        type: 'pdf',
        downloadUrl: 'https://example.com/presentation.pdf',
      ),
      FileItem(
        id: '3',
        name: 'Report.pdf',
        owner: 'Admin',
        size: '47Kb',
        date: '2013/03/03',
        type: 'pdf',
        downloadUrl: 'https://example.com/report.pdf',
      ),
      FileItem(
        id: '4',
        name: 'Spreadsheet.pdf',
        owner: 'Me',
        size: '1.2Mb',
        date: '2020/05/15',
        type: 'pdf',
        downloadUrl: 'https://example.com/spreadsheet.pdf',
      ),
      FileItem(
        id: '5',
        name: 'Manual.pdf',
        owner: 'Admin',
        size: '856Kb',
        date: '2018/11/22',
        type: 'pdf',
        downloadUrl: 'https://example.com/manual.pdf',
      ),
      FileItem(
        id: '6',
        name: 'Contract.pdf',
        owner: 'Me',
        size: '3.1Mb',
        date: '2021/07/08',
        type: 'pdf',
        downloadUrl: 'https://example.com/contract.pdf',
      ),
    ];
  }
}
